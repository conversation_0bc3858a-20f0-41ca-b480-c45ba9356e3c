class PropertyTour {
  final String id;
  final String propertyId;
  final String buyerId;
  final String buyerName;
  final String buyerEmail;
  final String buyerPhone;
  final String agentId;
  final String agentName;
  final DateTime scheduledDate;
  final TourType type;
  final TourStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime? completedAt;

  PropertyTour({
    required this.id,
    required this.propertyId,
    required this.buyerId,
    required this.buyerName,
    required this.buyerEmail,
    required this.buyerPhone,
    required this.agentId,
    required this.agentName,
    required this.scheduledDate,
    required this.type,
    required this.status,
    this.notes,
    required this.createdAt,
    this.completedAt,
  });

  factory PropertyTour.fromJson(Map<String, dynamic> json) {
    return PropertyTour(
      id: json['id'] ?? '',
      propertyId: json['propertyId'] ?? '',
      buyerId: json['buyerId'] ?? '',
      buyerName: json['buyerName'] ?? '',
      buyerEmail: json['buyerEmail'] ?? '',
      buyerPhone: json['buyerPhone'] ?? '',
      agentId: json['agentId'] ?? '',
      agentName: json['agentName'] ?? '',
      scheduledDate: DateTime.parse(json['scheduledDate']),
      type: TourType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TourType.inPerson,
      ),
      status: TourStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TourStatus.scheduled,
      ),
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'propertyId': propertyId,
      'buyerId': buyerId,
      'buyerName': buyerName,
      'buyerEmail': buyerEmail,
      'buyerPhone': buyerPhone,
      'agentId': agentId,
      'agentName': agentName,
      'scheduledDate': scheduledDate.toIso8601String(),
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }
}

enum TourType {
  inPerson,
  virtual,
  selfGuided,
}

enum TourStatus {
  scheduled,
  confirmed,
  inProgress,
  completed,
  cancelled,
  noShow,
}

extension TourTypeExtension on TourType {
  String get displayName {
    switch (this) {
      case TourType.inPerson:
        return 'In-Person';
      case TourType.virtual:
        return 'Virtual';
      case TourType.selfGuided:
        return 'Self-Guided';
    }
  }
}

extension TourStatusExtension on TourStatus {
  String get displayName {
    switch (this) {
      case TourStatus.scheduled:
        return 'Scheduled';
      case TourStatus.confirmed:
        return 'Confirmed';
      case TourStatus.inProgress:
        return 'In Progress';
      case TourStatus.completed:
        return 'Completed';
      case TourStatus.cancelled:
        return 'Cancelled';
      case TourStatus.noShow:
        return 'No Show';
    }
  }
}

class SavedSearch {
  final String id;
  final String userId;
  final String name;
  final String? location;
  final double? minPrice;
  final double? maxPrice;
  final int? minBedrooms;
  final int? maxBedrooms;
  final List<String> propertyTypes;
  final List<String> amenities;
  final bool alertsEnabled;
  final DateTime createdAt;
  final DateTime lastRun;
  final int resultCount;

  SavedSearch({
    required this.id,
    required this.userId,
    required this.name,
    this.location,
    this.minPrice,
    this.maxPrice,
    this.minBedrooms,
    this.maxBedrooms,
    required this.propertyTypes,
    required this.amenities,
    required this.alertsEnabled,
    required this.createdAt,
    required this.lastRun,
    required this.resultCount,
  });

  factory SavedSearch.fromJson(Map<String, dynamic> json) {
    return SavedSearch(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      name: json['name'] ?? '',
      location: json['location'],
      minPrice: json['minPrice']?.toDouble(),
      maxPrice: json['maxPrice']?.toDouble(),
      minBedrooms: json['minBedrooms'],
      maxBedrooms: json['maxBedrooms'],
      propertyTypes: List<String>.from(json['propertyTypes'] ?? []),
      amenities: List<String>.from(json['amenities'] ?? []),
      alertsEnabled: json['alertsEnabled'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      lastRun: DateTime.parse(json['lastRun']),
      resultCount: json['resultCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'location': location,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'minBedrooms': minBedrooms,
      'maxBedrooms': maxBedrooms,
      'propertyTypes': propertyTypes,
      'amenities': amenities,
      'alertsEnabled': alertsEnabled,
      'createdAt': createdAt.toIso8601String(),
      'lastRun': lastRun.toIso8601String(),
      'resultCount': resultCount,
    };
  }
}

class Lead {
  final String id;
  final String agentId;
  final String clientName;
  final String clientEmail;
  final String clientPhone;
  final LeadType type;
  final LeadStatus status;
  final LeadSource source;
  final String? propertyId;
  final String? notes;
  final double? budget;
  final List<String> preferredLocations;
  final DateTime createdAt;
  final DateTime lastContact;
  final DateTime? nextFollowUp;

  Lead({
    required this.id,
    required this.agentId,
    required this.clientName,
    required this.clientEmail,
    required this.clientPhone,
    required this.type,
    required this.status,
    required this.source,
    this.propertyId,
    this.notes,
    this.budget,
    required this.preferredLocations,
    required this.createdAt,
    required this.lastContact,
    this.nextFollowUp,
  });

  factory Lead.fromJson(Map<String, dynamic> json) {
    return Lead(
      id: json['id'] ?? '',
      agentId: json['agentId'] ?? '',
      clientName: json['clientName'] ?? '',
      clientEmail: json['clientEmail'] ?? '',
      clientPhone: json['clientPhone'] ?? '',
      type: LeadType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => LeadType.buyer,
      ),
      status: LeadStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => LeadStatus.new_,
      ),
      source: LeadSource.values.firstWhere(
        (e) => e.toString().split('.').last == json['source'],
        orElse: () => LeadSource.website,
      ),
      propertyId: json['propertyId'],
      notes: json['notes'],
      budget: json['budget']?.toDouble(),
      preferredLocations: List<String>.from(json['preferredLocations'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      lastContact: DateTime.parse(json['lastContact']),
      nextFollowUp: json['nextFollowUp'] != null ? DateTime.parse(json['nextFollowUp']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'clientName': clientName,
      'clientEmail': clientEmail,
      'clientPhone': clientPhone,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'source': source.toString().split('.').last,
      'propertyId': propertyId,
      'notes': notes,
      'budget': budget,
      'preferredLocations': preferredLocations,
      'createdAt': createdAt.toIso8601String(),
      'lastContact': lastContact.toIso8601String(),
      'nextFollowUp': nextFollowUp?.toIso8601String(),
    };
  }
}

enum LeadType {
  buyer,
  seller,
}

enum LeadStatus {
  new_,
  contacted,
  qualified,
  nurturing,
  converted,
  lost,
}

enum LeadSource {
  website,
  referral,
  socialMedia,
  advertising,
  walkIn,
  phone,
  email,
}
