import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/user.dart';
import '../models/property.dart';
import '../models/tour.dart';
import '../models/offer.dart';
import '../providers/user_provider.dart';
import '../providers/property_provider.dart';
import 'dashboard_router.dart';

class AgentDashboard extends BaseDashboard {
  const AgentDashboard({Key? key}) : super(key: key);

  @override
  State<AgentDashboard> createState() => _AgentDashboardState();
}

class _AgentDashboardState extends BaseDashboardState<AgentDashboard> {
  @override
  Widget buildAppBar(BuildContext context, String title, {List<Widget>? actions}) {
    return AppBar(
      title: Text(title),
      backgroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.agent),
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.person_add),
          onPressed: () => _addNewClient(context),
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(context),
        ),
        ...?actions,
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return Consumer2<UserProvider, PropertyProvider>(
      builder: (context, userProvider, propertyProvider, child) {
        final user = userProvider.currentUser!;
        
        return TabBarView(
          controller: tabController,
          children: [
            _buildOverviewTab(context, user, propertyProvider),
            _buildClientsTab(context, user, propertyProvider),
            _buildPropertiesTab(context, user, propertyProvider),
            _buildScheduleTab(context, user, propertyProvider),
            _buildProfileTab(context, user),
          ],
        );
      },
    );
  }

  Widget _buildOverviewTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(user),
          const SizedBox(height: 16),
          _buildPerformanceStats(),
          const SizedBox(height: 16),
          _buildCommissionChart(),
          const SizedBox(height: 16),
          _buildRecentActivity(),
          const SizedBox(height: 16),
          _buildUpcomingTasks(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(User user) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              DashboardNavigationHelper.getPrimaryColor(UserRole.agent),
              DashboardNavigationHelper.getPrimaryColor(UserRole.agent).withOpacity(0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back, ${user.name}!',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Manage your clients and close more deals',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _addNewClient(context),
                  icon: const Icon(Icons.person_add),
                  label: const Text('Add Client'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.agent),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () => _scheduleShowing(context),
                  icon: const Icon(Icons.schedule),
                  label: const Text('Schedule'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white.withOpacity(0.2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Active Clients',
            '24',
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Properties Listed',
            '18',
            Icons.home_work,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Deals Closed',
            '7',
            Icons.handshake,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Commission',
            '\$45K',
            Icons.attach_money,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommissionChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Monthly Commission',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 20,
                  barTouchData: BarTouchData(enabled: false),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                          return Text(months[value.toInt() % months.length]);
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: [
                    BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: 8, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                    BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: 10, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                    BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: 14, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                    BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: 15, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                    BarChartGroupData(x: 4, barRods: [BarChartRodData(toY: 13, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                    BarChartGroupData(x: 5, barRods: [BarChartRodData(toY: 10, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent))]),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'New client inquiry from John Smith',
              '1 hour ago',
              Icons.person_add,
              Colors.blue,
            ),
            _buildActivityItem(
              'Property showing scheduled for tomorrow',
              '3 hours ago',
              Icons.schedule,
              Colors.green,
            ),
            _buildActivityItem(
              'Offer accepted for Downtown Condo',
              '1 day ago',
              Icons.check_circle,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(time, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingTasks() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Upcoming Tasks',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => tabController.animateTo(3),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildTaskItem('Follow up with Sarah Johnson', 'Today 2:00 PM', true),
            _buildTaskItem('Property showing at Oak Street', 'Tomorrow 10:00 AM', false),
            _buildTaskItem('Contract review meeting', 'Friday 3:00 PM', false),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(String title, String time, bool isUrgent) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isUrgent ? Colors.red : Colors.blue,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(time, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientsTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search clients...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // Implement search
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: () => _addNewClient(context),
                icon: const Icon(Icons.add),
                label: const Text('Add Client'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 10, // Mock data
            itemBuilder: (context, index) {
              return _buildClientItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildClientItem(BuildContext context, int index) {
    final clientTypes = ['Buyer', 'Seller'];
    final clientType = clientTypes[index % 2];
    final isActive = index < 6;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: clientType == 'Buyer' ? Colors.blue[100] : Colors.green[100],
          child: Icon(
            clientType == 'Buyer' ? Icons.shopping_cart : Icons.sell,
            color: clientType == 'Buyer' ? Colors.blue[800] : Colors.green[800],
          ),
        ),
        title: Text('Client ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$clientType • Budget: \$${(500 + index * 100)}K'),
            Text(
              isActive ? 'Active' : 'Inactive',
              style: TextStyle(
                color: isActive ? Colors.green[600] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'contact', child: Text('Contact')),
            const PopupMenuItem(value: 'schedule', child: Text('Schedule Meeting')),
            const PopupMenuItem(value: 'notes', child: Text('View Notes')),
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
          ],
          onSelected: (value) => _handleClientAction(value, index),
        ),
        onTap: () => _showClientDetails(context, index),
      ),
    );
  }

  Widget _buildPropertiesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'My Properties',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Chip(
                label: const Text('18 Active'),
                backgroundColor: Colors.green[100],
                labelStyle: TextStyle(color: Colors.green[800]),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 8, // Mock data
            itemBuilder: (context, index) {
              return _buildPropertyItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyItem(BuildContext context, int index) {
    final statuses = ['Active', 'Pending', 'Sold'];
    final status = statuses[index % 3];
    final statusColors = {
      'Active': Colors.green,
      'Pending': Colors.orange,
      'Sold': Colors.blue,
    };
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[300],
          ),
          child: const Icon(Icons.home, color: Colors.grey),
        ),
        title: Text('Property ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('\$${(500000 + index * 50000).toString()}'),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: statusColors[status]!.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    status,
                    style: TextStyle(
                      color: statusColors[status],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text('${15 + index} views', style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('View Details')),
            const PopupMenuItem(value: 'edit', child: Text('Edit Listing')),
            const PopupMenuItem(value: 'analytics', child: Text('View Analytics')),
            const PopupMenuItem(value: 'schedule', child: Text('Schedule Showing')),
          ],
          onSelected: (value) => _handlePropertyAction(value, index),
        ),
        onTap: () => _showPropertyDetails(context, index),
      ),
    );
  }

  Widget _buildScheduleTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Schedule',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _scheduleShowing(context),
                icon: const Icon(Icons.add),
                label: const Text('Schedule'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 6, // Mock data
            itemBuilder: (context, index) {
              return _buildScheduleItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildScheduleItem(BuildContext context, int index) {
    final eventTypes = ['Property Showing', 'Client Meeting', 'Contract Review'];
    final eventType = eventTypes[index % 3];
    final isToday = index < 2;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isToday ? Colors.blue[100] : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            eventType == 'Property Showing' ? Icons.home : 
            eventType == 'Client Meeting' ? Icons.people : Icons.description,
            color: isToday ? Colors.blue[800] : Colors.grey[600],
          ),
        ),
        title: Text(eventType),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Client ${index + 1}'),
            Text(
              isToday ? 'Today ${10 + index}:00 AM' : 'Tomorrow ${2 + index}:00 PM',
              style: TextStyle(
                color: isToday ? Colors.blue[600] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'reschedule', child: Text('Reschedule')),
            const PopupMenuItem(value: 'cancel', child: Text('Cancel')),
            const PopupMenuItem(value: 'notes', child: Text('Add Notes')),
          ],
          onSelected: (value) => _handleScheduleAction(value, index),
        ),
        onTap: () => _showScheduleDetails(context, index),
      ),
    );
  }

  void _addNewClient(BuildContext context) {
    // Add new client
  }

  void _showNotifications(BuildContext context) {
    // Show notifications
  }

  void _scheduleShowing(BuildContext context) {
    // Schedule showing
  }

  void _handleClientAction(String action, int index) {
    // Handle client actions
  }

  void _showClientDetails(BuildContext context, int index) {
    // Show client details
  }

  void _handlePropertyAction(String action, int index) {
    // Handle property actions
  }

  void _showPropertyDetails(BuildContext context, int index) {
    // Show property details
  }

  void _handleScheduleAction(String action, int index) {
    // Handle schedule actions
  }

  void _showScheduleDetails(BuildContext context, int index) {
    // Show schedule details
  }

  Widget _buildProfileTab(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProfileHeader(user),
          const SizedBox(height: 24),
          _buildProfileSettings(),
          const SizedBox(height: 24),
          _buildAccountActions(context),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(User user) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.agent),
              child: Text(
                user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      user.role.displayName,
                      style: TextStyle(
                        color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _editProfile(context),
              icon: const Icon(Icons.edit),
              tooltip: 'Edit Profile',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Settings',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Notifications',
              'Manage your notification preferences',
              Icons.notifications_outlined,
              () => _manageNotifications(context),
            ),
            _buildSettingItem(
              'Commission Settings',
              'Configure your commission rates',
              Icons.monetization_on_outlined,
              () => _manageCommission(context),
            ),
            _buildSettingItem(
              'Privacy',
              'Control your privacy settings',
              Icons.privacy_tip_outlined,
              () => _managePrivacy(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: DashboardNavigationHelper.getPrimaryColor(UserRole.agent)),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildAccountActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Account',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[50],
                  foregroundColor: Colors.red[700],
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _editProfile(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit profile functionality coming soon')),
    );
  }

  void _manageNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification settings coming soon')),
    );
  }

  void _manageCommission(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Commission settings coming soon')),
    );
  }

  void _managePrivacy(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Privacy settings coming soon')),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<UserProvider>().logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
