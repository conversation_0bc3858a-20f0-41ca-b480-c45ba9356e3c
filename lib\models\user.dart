import 'property.dart';

class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String? profileImage;
  final UserRole role;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final List<String> favoritePropertyIds;
  final UserPreferences preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    this.profileImage,
    required this.role,
    required this.createdAt,
    required this.lastLoginAt,
    required this.favoritePropertyIds,
    required this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profileImage: json['profileImage'],
      role: UserRole.values.firstWhere(
        (e) => e.toString().split('.').last == json['role'],
        orElse: () => UserRole.buyer,
      ),
      createdAt: (json['createdAt'] as dynamic).toDate(),
      lastLoginAt: (json['lastLoginAt'] as dynamic).toDate(),
      favoritePropertyIds: List<String>.from(json['favoritePropertyIds'] ?? []),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
      'role': role.toString().split('.').last,
      'createdAt': createdAt,
      'lastLoginAt': lastLoginAt,
      'favoritePropertyIds': favoritePropertyIds,
      'preferences': preferences.toJson(),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImage,
    UserRole? role,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<String>? favoritePropertyIds,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      favoritePropertyIds: favoritePropertyIds ?? this.favoritePropertyIds,
      preferences: preferences ?? this.preferences,
    );
  }
}

enum UserRole {
  buyer,
  seller,
  agent,
  admin,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.buyer:
        return 'Buyer';
      case UserRole.seller:
        return 'Seller';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Admin';
      default:
        return 'Unknown';
    }
  }
}

class UserPreferences {
  final bool darkMode;
  final bool notifications;
  final String currency;
  final String language;
  final double? minPrice;
  final double? maxPrice;
  final List<String> preferredLocations;
  final List<PropertyType> preferredPropertyTypes;

  UserPreferences({
    this.darkMode = false,
    this.notifications = true,
    this.currency = 'USD',
    this.language = 'en',
    this.minPrice,
    this.maxPrice,
    this.preferredLocations = const [],
    this.preferredPropertyTypes = const [],
  });

  factory UserPreferences.initial() => UserPreferences();

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      darkMode: json['darkMode'] ?? false,
      notifications: json['notifications'] ?? true,
      currency: json['currency'] ?? 'USD',
      language: json['language'] ?? 'en',
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      preferredLocations: List<String>.from(json['preferredLocations'] ?? []),
      preferredPropertyTypes: (json['preferredPropertyTypes'] as List<dynamic>?)
              ?.map((e) => PropertyType.values.firstWhere(
                    (type) => type.toString().split('.').last == e,
                    orElse: () => PropertyType.house,
                  ))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'notifications': notifications,
      'currency': currency,
      'language': language,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'preferredLocations': preferredLocations,
      'preferredPropertyTypes':
          preferredPropertyTypes.map((e) => e.toString().split('.').last).toList(),
    };
  }

  UserPreferences copyWith({
    bool? darkMode,
    bool? notifications,
    String? currency,
    String? language,
    double? minPrice,
    double? maxPrice,
    List<String>? preferredLocations,
    List<PropertyType>? preferredPropertyTypes,
  }) {
    return UserPreferences(
      darkMode: darkMode ?? this.darkMode,
      notifications: notifications ?? this.notifications,
      currency: currency ?? this.currency,
      language: language ?? this.language,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      preferredLocations: preferredLocations ?? this.preferredLocations,
      preferredPropertyTypes: preferredPropertyTypes ?? this.preferredPropertyTypes,
    );
  }
}
