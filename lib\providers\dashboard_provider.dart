import 'package:flutter/foundation.dart';
import '../models/analytics.dart';
import '../models/tour.dart';
import '../models/offer.dart';
import '../models/user.dart';
import '../services/dashboard_service.dart';

class DashboardProvider with ChangeNotifier {
  final DashboardService _dashboardService = DashboardService();

  // Analytics
  DashboardStats? _dashboardStats;
  Map<String, PropertyAnalytics> _propertyAnalytics = {};
  bool _isLoadingStats = false;
  bool _isLoadingAnalytics = false;

  // Tours
  List<PropertyTour> _userTours = [];
  List<PropertyTour> _agentTours = [];
  bool _isLoadingTours = false;

  // Saved Searches
  List<SavedSearch> _savedSearches = [];
  bool _isLoadingSearches = false;

  // Leads
  List<Lead> _leads = [];
  bool _isLoadingLeads = false;

  // Offers
  List<PropertyOffer> _offers = [];
  bool _isLoadingOffers = false;

  // Commissions
  List<Commission> _commissions = [];
  bool _isLoadingCommissions = false;

  // Inquiries
  Map<String, List<InquiryData>> _propertyInquiries = {};
  bool _isLoadingInquiries = false;

  // Getters
  DashboardStats? get dashboardStats => _dashboardStats;
  Map<String, PropertyAnalytics> get propertyAnalytics => _propertyAnalytics;
  List<PropertyTour> get userTours => _userTours;
  List<PropertyTour> get agentTours => _agentTours;
  List<SavedSearch> get savedSearches => _savedSearches;
  List<Lead> get leads => _leads;
  List<PropertyOffer> get offers => _offers;
  List<Commission> get commissions => _commissions;
  Map<String, List<InquiryData>> get propertyInquiries => _propertyInquiries;

  bool get isLoadingStats => _isLoadingStats;
  bool get isLoadingAnalytics => _isLoadingAnalytics;
  bool get isLoadingTours => _isLoadingTours;
  bool get isLoadingSearches => _isLoadingSearches;
  bool get isLoadingLeads => _isLoadingLeads;
  bool get isLoadingOffers => _isLoadingOffers;
  bool get isLoadingCommissions => _isLoadingCommissions;
  bool get isLoadingInquiries => _isLoadingInquiries;

  // Analytics Methods
  Future<void> loadDashboardStats(String userId, UserRole role) async {
    _isLoadingStats = true;
    notifyListeners();

    try {
      _dashboardStats = await _dashboardService.getDashboardStats(userId, role);
    } catch (e) {
      print('Error loading dashboard stats: $e');
    } finally {
      _isLoadingStats = false;
      notifyListeners();
    }
  }

  Future<void> loadPropertyAnalytics(String propertyId) async {
    _isLoadingAnalytics = true;
    notifyListeners();

    try {
      final analytics = await _dashboardService.getPropertyAnalytics(propertyId);
      if (analytics != null) {
        _propertyAnalytics[propertyId] = analytics;
      }
    } catch (e) {
      print('Error loading property analytics: $e');
    } finally {
      _isLoadingAnalytics = false;
      notifyListeners();
    }
  }

  Future<void> recordPropertyView(String propertyId, String userId, String source) async {
    try {
      await _dashboardService.recordPropertyView(propertyId, userId, source);
      // Reload analytics for this property
      await loadPropertyAnalytics(propertyId);
    } catch (e) {
      print('Error recording property view: $e');
    }
  }

  // Tour Methods
  Future<void> loadUserTours(String userId) async {
    _isLoadingTours = true;
    notifyListeners();

    try {
      _userTours = await _dashboardService.getUserTours(userId);
    } catch (e) {
      print('Error loading user tours: $e');
    } finally {
      _isLoadingTours = false;
      notifyListeners();
    }
  }

  Future<void> loadAgentTours(String agentId) async {
    _isLoadingTours = true;
    notifyListeners();

    try {
      _agentTours = await _dashboardService.getAgentTours(agentId);
    } catch (e) {
      print('Error loading agent tours: $e');
    } finally {
      _isLoadingTours = false;
      notifyListeners();
    }
  }

  Future<void> scheduleTour(PropertyTour tour) async {
    try {
      await _dashboardService.scheduleTour(tour);
      // Reload tours
      if (tour.buyerId.isNotEmpty) {
        await loadUserTours(tour.buyerId);
      }
      if (tour.agentId.isNotEmpty) {
        await loadAgentTours(tour.agentId);
      }
    } catch (e) {
      print('Error scheduling tour: $e');
    }
  }

  Future<void> updateTourStatus(String tourId, TourStatus status, String userId, UserRole role) async {
    try {
      await _dashboardService.updateTourStatus(tourId, status);
      // Reload tours based on user role
      if (role == UserRole.buyer) {
        await loadUserTours(userId);
      } else if (role == UserRole.agent) {
        await loadAgentTours(userId);
      }
    } catch (e) {
      print('Error updating tour status: $e');
    }
  }

  // Saved Search Methods
  Future<void> loadSavedSearches(String userId) async {
    _isLoadingSearches = true;
    notifyListeners();

    try {
      _savedSearches = await _dashboardService.getUserSavedSearches(userId);
    } catch (e) {
      print('Error loading saved searches: $e');
    } finally {
      _isLoadingSearches = false;
      notifyListeners();
    }
  }

  Future<void> saveSavedSearch(SavedSearch savedSearch) async {
    try {
      await _dashboardService.saveSavedSearch(savedSearch);
      await loadSavedSearches(savedSearch.userId);
    } catch (e) {
      print('Error saving search: $e');
    }
  }

  Future<void> deleteSavedSearch(String searchId, String userId) async {
    try {
      await _dashboardService.deleteSavedSearch(searchId);
      await loadSavedSearches(userId);
    } catch (e) {
      print('Error deleting saved search: $e');
    }
  }

  // Lead Methods
  Future<void> loadAgentLeads(String agentId) async {
    _isLoadingLeads = true;
    notifyListeners();

    try {
      _leads = await _dashboardService.getAgentLeads(agentId);
    } catch (e) {
      print('Error loading agent leads: $e');
    } finally {
      _isLoadingLeads = false;
      notifyListeners();
    }
  }

  Future<void> createLead(Lead lead) async {
    try {
      await _dashboardService.createLead(lead);
      await loadAgentLeads(lead.agentId);
    } catch (e) {
      print('Error creating lead: $e');
    }
  }

  Future<void> updateLeadStatus(String leadId, LeadStatus status, String agentId) async {
    try {
      await _dashboardService.updateLeadStatus(leadId, status);
      await loadAgentLeads(agentId);
    } catch (e) {
      print('Error updating lead status: $e');
    }
  }

  // Offer Methods
  Future<void> loadPropertyOffers(String propertyId) async {
    _isLoadingOffers = true;
    notifyListeners();

    try {
      _offers = await _dashboardService.getPropertyOffers(propertyId);
    } catch (e) {
      print('Error loading property offers: $e');
    } finally {
      _isLoadingOffers = false;
      notifyListeners();
    }
  }

  Future<void> loadBuyerOffers(String buyerId) async {
    _isLoadingOffers = true;
    notifyListeners();

    try {
      _offers = await _dashboardService.getBuyerOffers(buyerId);
    } catch (e) {
      print('Error loading buyer offers: $e');
    } finally {
      _isLoadingOffers = false;
      notifyListeners();
    }
  }

  Future<void> submitOffer(PropertyOffer offer) async {
    try {
      await _dashboardService.submitOffer(offer);
      await loadBuyerOffers(offer.buyerId);
    } catch (e) {
      print('Error submitting offer: $e');
    }
  }

  Future<void> updateOfferStatus(String offerId, OfferStatus status, String userId, UserRole role, {String? counterOfferAmount, String? rejectionReason}) async {
    try {
      await _dashboardService.updateOfferStatus(offerId, status, counterOfferAmount: counterOfferAmount, rejectionReason: rejectionReason);
      
      // Reload offers based on user role
      if (role == UserRole.buyer) {
        await loadBuyerOffers(userId);
      } else {
        // For sellers/agents, we'd need the property ID to reload property offers
        // This would be implemented based on the specific use case
      }
    } catch (e) {
      print('Error updating offer status: $e');
    }
  }

  // Commission Methods
  Future<void> loadAgentCommissions(String agentId) async {
    _isLoadingCommissions = true;
    notifyListeners();

    try {
      _commissions = await _dashboardService.getAgentCommissions(agentId);
    } catch (e) {
      print('Error loading agent commissions: $e');
    } finally {
      _isLoadingCommissions = false;
      notifyListeners();
    }
  }

  Future<void> createCommission(Commission commission) async {
    try {
      await _dashboardService.createCommission(commission);
      await loadAgentCommissions(commission.agentId);
    } catch (e) {
      print('Error creating commission: $e');
    }
  }

  Future<void> updateCommissionStatus(String commissionId, CommissionStatus status, String agentId) async {
    try {
      await _dashboardService.updateCommissionStatus(commissionId, status);
      await loadAgentCommissions(agentId);
    } catch (e) {
      print('Error updating commission status: $e');
    }
  }

  // Inquiry Methods
  Future<void> loadPropertyInquiries(String propertyId) async {
    _isLoadingInquiries = true;
    notifyListeners();

    try {
      final inquiries = await _dashboardService.getPropertyInquiries(propertyId);
      _propertyInquiries[propertyId] = inquiries;
    } catch (e) {
      print('Error loading property inquiries: $e');
    } finally {
      _isLoadingInquiries = false;
      notifyListeners();
    }
  }

  Future<void> submitInquiry(InquiryData inquiry, String propertyId) async {
    try {
      await _dashboardService.submitInquiry(inquiry, propertyId);
      await loadPropertyInquiries(propertyId);
    } catch (e) {
      print('Error submitting inquiry: $e');
    }
  }

  // Utility Methods
  void clearData() {
    _dashboardStats = null;
    _propertyAnalytics.clear();
    _userTours.clear();
    _agentTours.clear();
    _savedSearches.clear();
    _leads.clear();
    _offers.clear();
    _commissions.clear();
    _propertyInquiries.clear();
    notifyListeners();
  }

  List<PropertyTour> getUpcomingTours() {
    final now = DateTime.now();
    return _userTours.where((tour) => 
      tour.scheduledDate.isAfter(now) && 
      (tour.status == TourStatus.scheduled || tour.status == TourStatus.confirmed)
    ).toList();
  }

  List<Lead> getActiveLeads() {
    return _leads.where((lead) => 
      lead.status != LeadStatus.converted && 
      lead.status != LeadStatus.lost
    ).toList();
  }

  List<PropertyOffer> getPendingOffers() {
    return _offers.where((offer) => offer.status == OfferStatus.pending).toList();
  }

  double getTotalCommissionEarned() {
    return _commissions
        .where((commission) => commission.status == CommissionStatus.paid)
        .fold(0.0, (sum, commission) => sum + commission.agentSplit);
  }

  double getPendingCommission() {
    return _commissions
        .where((commission) => commission.status == CommissionStatus.approved)
        .fold(0.0, (sum, commission) => sum + commission.agentSplit);
  }
}
