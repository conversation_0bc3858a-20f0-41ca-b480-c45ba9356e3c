import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user.dart';
import '../providers/user_provider.dart';
import 'seller_dashboard.dart';
import 'buyer_dashboard.dart';
import 'agent_dashboard.dart';
import 'admin_dashboard.dart';
import '../auth/auth_screen.dart';

class DashboardRouter extends StatelessWidget {
  const DashboardRouter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        if (!userProvider.isAuthenticated || userProvider.currentUser == null) {
          return const AuthScreen();
        }

        final user = userProvider.currentUser!;
        
        switch (user.role) {
          case UserRole.seller:
            return const SellerDashboard();
          case UserRole.buyer:
            return const BuyerDashboard();
          case UserRole.agent:
            return const AgentDashboard();
          case UserRole.admin:
            return const AdminDashboard();
          default:
            return const BuyerDashboard(); // Default fallback
        }
      },
    );
  }
}

class DashboardNavigationHelper {
  static String getDashboardRoute(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return '/seller_dashboard';
      case UserRole.buyer:
        return '/buyer_dashboard';
      case UserRole.agent:
        return '/agent_dashboard';
      case UserRole.admin:
        return '/admin_dashboard';
      default:
        return '/buyer_dashboard';
    }
  }

  static Widget getDashboardWidget(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return const SellerDashboard();
      case UserRole.buyer:
        return const BuyerDashboard();
      case UserRole.agent:
        return const AgentDashboard();
      case UserRole.admin:
        return const AdminDashboard();
      default:
        return const BuyerDashboard();
    }
  }

  static List<NavigationDestination> getNavigationDestinations(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return [
          const NavigationDestination(
            icon: Icon(Icons.dashboard_outlined),
            selectedIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const NavigationDestination(
            icon: Icon(Icons.home_work_outlined),
            selectedIcon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          const NavigationDestination(
            icon: Icon(Icons.analytics_outlined),
            selectedIcon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          const NavigationDestination(
            icon: Icon(Icons.message_outlined),
            selectedIcon: Icon(Icons.message),
            label: 'Inquiries',
          ),
          const NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      case UserRole.buyer:
        return [
          const NavigationDestination(
            icon: Icon(Icons.dashboard_outlined),
            selectedIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const NavigationDestination(
            icon: Icon(Icons.search_outlined),
            selectedIcon: Icon(Icons.search),
            label: 'Search',
          ),
          const NavigationDestination(
            icon: Icon(Icons.favorite_border),
            selectedIcon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          const NavigationDestination(
            icon: Icon(Icons.tour_outlined),
            selectedIcon: Icon(Icons.tour),
            label: 'Tours',
          ),
          const NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      case UserRole.agent:
        return [
          const NavigationDestination(
            icon: Icon(Icons.dashboard_outlined),
            selectedIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const NavigationDestination(
            icon: Icon(Icons.people_outline),
            selectedIcon: Icon(Icons.people),
            label: 'Clients',
          ),
          const NavigationDestination(
            icon: Icon(Icons.home_work_outlined),
            selectedIcon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          const NavigationDestination(
            icon: Icon(Icons.schedule_outlined),
            selectedIcon: Icon(Icons.schedule),
            label: 'Schedule',
          ),
          const NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
      case UserRole.admin:
        return [
          const NavigationDestination(
            icon: Icon(Icons.dashboard_outlined),
            selectedIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const NavigationDestination(
            icon: Icon(Icons.people_outline),
            selectedIcon: Icon(Icons.people),
            label: 'Users',
          ),
          const NavigationDestination(
            icon: Icon(Icons.home_work_outlined),
            selectedIcon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          const NavigationDestination(
            icon: Icon(Icons.analytics_outlined),
            selectedIcon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          const NavigationDestination(
            icon: Icon(Icons.settings_outlined),
            selectedIcon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ];
      default:
        return [
          const NavigationDestination(
            icon: Icon(Icons.dashboard_outlined),
            selectedIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          const NavigationDestination(
            icon: Icon(Icons.search_outlined),
            selectedIcon: Icon(Icons.search),
            label: 'Search',
          ),
          const NavigationDestination(
            icon: Icon(Icons.favorite_border),
            selectedIcon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          const NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ];
    }
  }

  static List<String> getTabLabels(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return ['Overview', 'Properties', 'Analytics', 'Inquiries', 'Profile'];
      case UserRole.buyer:
        return ['Overview', 'Searches', 'Favorites', 'Tours', 'Profile'];
      case UserRole.agent:
        return ['Overview', 'Clients', 'Properties', 'Schedule', 'Profile'];
      case UserRole.admin:
        return ['Overview', 'Users', 'Properties', 'Analytics', 'Settings'];
      default:
        return ['Overview', 'Search', 'Favorites'];
    }
  }

  static Color getPrimaryColor(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return const Color(0xFF4CAF50); // Green
      case UserRole.buyer:
        return const Color(0xFF2196F3); // Blue
      case UserRole.agent:
        return const Color(0xFF9C27B0); // Purple
      case UserRole.admin:
        return const Color(0xFFFF5722); // Deep Orange
      default:
        return const Color(0xFF6366F1); // Indigo
    }
  }

  static IconData getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return Icons.sell;
      case UserRole.buyer:
        return Icons.shopping_cart;
      case UserRole.agent:
        return Icons.business_center;
      case UserRole.admin:
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }
}

abstract class BaseDashboard extends StatefulWidget {
  const BaseDashboard({Key? key}) : super(key: key);
}

abstract class BaseDashboardState<T extends BaseDashboard> extends State<T>
    with TickerProviderStateMixin {
  late TabController tabController;
  late AnimationController animationController;
  late Animation<double> fadeAnimation;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    final userRole = context.read<UserProvider>().currentUser?.role ?? UserRole.buyer;
    final tabCount = DashboardNavigationHelper.getTabLabels(userRole).length;

    tabController = TabController(length: tabCount, vsync: this);

    // Add listener to sync bottom navigation with tab changes
    tabController.addListener(() {
      if (tabController.indexIsChanging || tabController.index != selectedIndex) {
        setState(() {
          selectedIndex = tabController.index;
        });
      }
    });

    animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
    );

    animationController.forward();
  }

  @override
  void dispose() {
    tabController.dispose();
    animationController.dispose();
    super.dispose();
  }

  void onNavigationTapped(int index) {
    setState(() {
      selectedIndex = index;
    });
    // Sync the TabController with the bottom navigation selection
    if (tabController.index != index) {
      tabController.animateTo(index);
    }
  }

  Widget buildAppBar(BuildContext context, String title, {List<Widget>? actions});
  Widget buildBody(BuildContext context);
  
  @override
  Widget build(BuildContext context) {
    final userProvider = context.watch<UserProvider>();
    final user = userProvider.currentUser;
    
    if (user == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: buildAppBar(context, '${user.role.displayName} Dashboard') as PreferredSizeWidget?,
      body: FadeTransition(
        opacity: fadeAnimation,
        child: buildBody(context),
      ),
      bottomNavigationBar: NavigationBar(
        destinations: DashboardNavigationHelper.getNavigationDestinations(user.role),
        onDestinationSelected: onNavigationTapped,
        selectedIndex: selectedIndex,
      ),
    );
  }
}
