import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user.dart';
import '../models/property.dart';
import '../models/tour.dart';
import '../models/offer.dart';
import '../providers/user_provider.dart';
import '../providers/property_provider.dart';
import 'dashboard_router.dart';
import '../widgets/property_card_widgets.dart';

class BuyerDashboard extends BaseDashboard {
  const BuyerDashboard({Key? key}) : super(key: key);

  @override
  State<BuyerDashboard> createState() => _BuyerDashboardState();
}

class _BuyerDashboardState extends BaseDashboardState<BuyerDashboard> {
  @override
  Widget buildAppBar(BuildContext context, String title, {List<Widget>? actions}) {
    return AppBar(
      title: Text(title),
      backgroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.buyer),
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => Navigator.pushNamed(context, '/listings'),
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(context),
        ),
        ...?actions,
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return Consumer2<UserProvider, PropertyProvider>(
      builder: (context, userProvider, propertyProvider, child) {
        final user = userProvider.currentUser!;
        
        return TabBarView(
          controller: tabController,
          children: [
            _buildOverviewTab(context, user, propertyProvider),
            _buildSearchesTab(context, user, propertyProvider),
            _buildFavoritesTab(context, user, propertyProvider),
            _buildToursTab(context, user, propertyProvider),
          ],
        );
      },
    );
  }

  Widget _buildOverviewTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(user),
          const SizedBox(height: 16),
          _buildQuickStats(),
          const SizedBox(height: 16),
          _buildRecentlyViewed(propertyProvider),
          const SizedBox(height: 16),
          _buildRecommendations(propertyProvider),
          const SizedBox(height: 16),
          _buildMortgageCalculator(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(User user) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              DashboardNavigationHelper.getPrimaryColor(UserRole.buyer),
              DashboardNavigationHelper.getPrimaryColor(UserRole.buyer).withOpacity(0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back, ${user.name}!',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Find your dream home today',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/listings'),
              icon: const Icon(Icons.search),
              label: const Text('Start Searching'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.buyer),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Saved Searches',
            '5',
            Icons.bookmark,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Favorites',
            '23',
            Icons.favorite,
            Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Tours Scheduled',
            '3',
            Icons.tour,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Offers Made',
            '1',
            Icons.local_offer,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentlyViewed(PropertyProvider propertyProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recently Viewed',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _showViewingHistory(context),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // Mock data
            itemBuilder: (context, index) {
              return Container(
                width: 160,
                margin: const EdgeInsets.only(right: 12),
                child: Card(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                            color: Colors.grey[300],
                          ),
                          child: const Center(
                            child: Icon(Icons.home, size: 40, color: Colors.grey),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Property ${index + 1}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              '\$${(500000 + index * 50000).toString()}',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendations(PropertyProvider propertyProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recommended for You',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // Mock data
            itemBuilder: (context, index) {
              return Container(
                width: 160,
                margin: const EdgeInsets.only(right: 12),
                child: Card(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                            color: Colors.grey[300],
                          ),
                          child: Stack(
                            children: [
                              const Center(
                                child: Icon(Icons.home, size: 40, color: Colors.grey),
                              ),
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.star,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Recommended ${index + 1}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              '\$${(600000 + index * 75000).toString()}',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMortgageCalculator() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Mortgage Calculator',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _showMortgageCalculator(context),
                  child: const Text('Open Calculator'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Estimate your monthly payments and see what you can afford.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _showMortgageCalculator(context),
              icon: const Icon(Icons.calculate),
              label: const Text('Calculate Payments'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Saved Searches',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _createNewSearch(context),
                icon: const Icon(Icons.add),
                label: const Text('New Search'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5, // Mock data
            itemBuilder: (context, index) {
              return _buildSavedSearchItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSavedSearchItem(BuildContext context, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.search, color: Colors.blue[800]),
        ),
        title: Text('Search ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Downtown • \$500K-\$800K • 2-3 bed'),
            Text('${12 + index} new results', style: TextStyle(color: Colors.green[600])),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: index % 2 == 0,
              onChanged: (value) => _toggleSearchAlerts(index, value),
            ),
            PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'run', child: Text('Run Search')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
              onSelected: (value) => _handleSearchAction(value, index),
            ),
          ],
        ),
        onTap: () => _runSavedSearch(context, index),
      ),
    );
  }

  Widget _buildFavoritesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Favorite Properties',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Chip(
                label: const Text('23 Properties'),
                backgroundColor: Colors.red[100],
                labelStyle: TextStyle(color: Colors.red[800]),
              ),
            ],
          ),
        ),
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: 8, // Mock data
            itemBuilder: (context, index) {
              return _buildFavoritePropertyCard(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFavoritePropertyCard(BuildContext context, int index) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                color: Colors.grey[300],
              ),
              child: Stack(
                children: [
                  const Center(
                    child: Icon(Icons.home, size: 40, color: Colors.grey),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: IconButton(
                      onPressed: () => _removeFavorite(index),
                      icon: const Icon(Icons.favorite, color: Colors.red),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.white,
                        padding: const EdgeInsets.all(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Property ${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '\$${(500000 + index * 50000).toString()}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                Text(
                  '3 bed • 2 bath',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToursTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Property Tours',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _scheduleNewTour(context),
                icon: const Icon(Icons.add),
                label: const Text('Schedule Tour'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5, // Mock data
            itemBuilder: (context, index) {
              return _buildTourItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTourItem(BuildContext context, int index) {
    final isUpcoming = index < 2;
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isUpcoming ? Colors.green[100] : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.tour,
            color: isUpcoming ? Colors.green[800] : Colors.grey[600],
          ),
        ),
        title: Text('Modern Villa Tour'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Tomorrow at 2:00 PM'),
            Text(
              isUpcoming ? 'Upcoming' : 'Completed',
              style: TextStyle(
                color: isUpcoming ? Colors.green[600] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            if (isUpcoming) ...[
              const PopupMenuItem(value: 'reschedule', child: Text('Reschedule')),
              const PopupMenuItem(value: 'cancel', child: Text('Cancel')),
            ] else ...[
              const PopupMenuItem(value: 'feedback', child: Text('Leave Feedback')),
              const PopupMenuItem(value: 'schedule_again', child: Text('Schedule Again')),
            ],
          ],
          onSelected: (value) => _handleTourAction(value, index),
        ),
        onTap: () => _showTourDetails(context, index),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    // Show notifications
  }

  void _showViewingHistory(BuildContext context) {
    // Show viewing history
  }

  void _showMortgageCalculator(BuildContext context) {
    // Show mortgage calculator
  }

  void _createNewSearch(BuildContext context) {
    // Create new search
  }

  void _toggleSearchAlerts(int index, bool value) {
    // Toggle search alerts
  }

  void _handleSearchAction(String action, int index) {
    // Handle search actions
  }

  void _runSavedSearch(BuildContext context, int index) {
    // Run saved search
  }

  void _removeFavorite(int index) {
    // Remove from favorites
  }

  void _scheduleNewTour(BuildContext context) {
    // Schedule new tour
  }

  void _handleTourAction(String action, int index) {
    // Handle tour actions
  }

  void _showTourDetails(BuildContext context, int index) {
    // Show tour details
  }
}
