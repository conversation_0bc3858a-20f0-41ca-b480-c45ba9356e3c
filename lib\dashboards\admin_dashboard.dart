import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/user.dart';
import '../models/property.dart';
import '../models/analytics.dart';
import '../providers/user_provider.dart';
import '../providers/property_provider.dart';
import 'dashboard_router.dart';

class AdminDashboard extends BaseDashboard {
  const AdminDashboard({Key? key}) : super(key: key);

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends BaseDashboardState<AdminDashboard> {
  @override
  Widget buildAppBar(BuildContext context, String title, {List<Widget>? actions}) {
    return AppBar(
      title: Text(title),
      backgroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.admin),
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.backup),
          onPressed: () => _exportData(context),
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(context),
        ),
        ...?actions,
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return Consumer2<UserProvider, PropertyProvider>(
      builder: (context, userProvider, propertyProvider, child) {
        final user = userProvider.currentUser!;
        
        return TabBarView(
          controller: tabController,
          children: [
            _buildOverviewTab(context, user, propertyProvider),
            _buildUsersTab(context, user, propertyProvider),
            _buildPropertiesTab(context, user, propertyProvider),
            _buildAnalyticsTab(context, user, propertyProvider),
            _buildSettingsTab(context, user),
          ],
        );
      },
    );
  }

  Widget _buildOverviewTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(user),
          const SizedBox(height: 16),
          _buildPlatformStats(),
          const SizedBox(height: 16),
          _buildRevenueChart(),
          const SizedBox(height: 16),
          _buildRecentActivity(),
          const SizedBox(height: 16),
          _buildSystemHealth(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(User user) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              DashboardNavigationHelper.getPrimaryColor(UserRole.admin),
              DashboardNavigationHelper.getPrimaryColor(UserRole.admin).withOpacity(0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back, ${user.name}!',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Monitor and manage the platform',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _generateReport(context),
                  icon: const Icon(Icons.analytics),
                  label: const Text('Generate Report'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: DashboardNavigationHelper.getPrimaryColor(UserRole.admin),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () => _systemSettings(context),
                  icon: const Icon(Icons.settings),
                  label: const Text('Settings'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white.withOpacity(0.2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlatformStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Users',
            '2,847',
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Active Properties',
            '1,234',
            Icons.home_work,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Monthly Revenue',
            '\$125K',
            Icons.attach_money,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Transactions',
            '89',
            Icons.receipt,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Platform Revenue',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: false),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                          return Text(months[value.toInt() % months.length]);
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: [
                        const FlSpot(0, 80),
                        const FlSpot(1, 95),
                        const FlSpot(2, 110),
                        const FlSpot(3, 125),
                        const FlSpot(4, 140),
                        const FlSpot(5, 125),
                      ],
                      isCurved: true,
                      color: DashboardNavigationHelper.getPrimaryColor(UserRole.admin),
                      barWidth: 3,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: DashboardNavigationHelper.getPrimaryColor(UserRole.admin).withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Platform Activity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'New user registration: John Doe',
              '15 minutes ago',
              Icons.person_add,
              Colors.blue,
            ),
            _buildActivityItem(
              'Property listing approved',
              '1 hour ago',
              Icons.check_circle,
              Colors.green,
            ),
            _buildActivityItem(
              'Payment processed: \$2,500',
              '2 hours ago',
              Icons.payment,
              Colors.orange,
            ),
            _buildActivityItem(
              'System backup completed',
              '6 hours ago',
              Icons.backup,
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(time, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealth() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Health',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildHealthMetric('Server Uptime', '99.9%', Colors.green),
            _buildHealthMetric('Database Performance', '95%', Colors.green),
            _buildHealthMetric('API Response Time', '120ms', Colors.orange),
            _buildHealthMetric('Storage Usage', '67%', Colors.blue),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthMetric(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search users...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    // Implement search
                  },
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                onPressed: () => _showUserFilters(context),
                icon: const Icon(Icons.filter_list),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 15, // Mock data
            itemBuilder: (context, index) {
              return _buildUserItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUserItem(BuildContext context, int index) {
    final roles = [UserRole.buyer, UserRole.seller, UserRole.agent];
    final role = roles[index % 3];
    final isActive = index % 4 != 0;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: DashboardNavigationHelper.getPrimaryColor(role).withOpacity(0.1),
          child: Icon(
            DashboardNavigationHelper.getRoleIcon(role),
            color: DashboardNavigationHelper.getPrimaryColor(role),
          ),
        ),
        title: Text('User ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${role.displayName} • user${index + 1}@email.com'),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    isActive ? 'Active' : 'Suspended',
                    style: TextStyle(
                      color: isActive ? Colors.green[800] : Colors.red[800],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text('Joined ${30 - index} days ago', style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('View Profile')),
            PopupMenuItem(
              value: isActive ? 'suspend' : 'activate',
              child: Text(isActive ? 'Suspend' : 'Activate'),
            ),
            const PopupMenuItem(value: 'reset_password', child: Text('Reset Password')),
            const PopupMenuItem(value: 'delete', child: Text('Delete Account')),
          ],
          onSelected: (value) => _handleUserAction(value, index),
        ),
        onTap: () => _showUserDetails(context, index),
      ),
    );
  }

  Widget _buildPropertiesTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Property Management',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Chip(
                label: const Text('12 Pending'),
                backgroundColor: Colors.orange[100],
                labelStyle: TextStyle(color: Colors.orange[800]),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 10, // Mock data
            itemBuilder: (context, index) {
              return _buildPropertyManagementItem(context, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyManagementItem(BuildContext context, int index) {
    final statuses = ['Pending Review', 'Approved', 'Rejected', 'Active'];
    final status = statuses[index % 4];
    final statusColors = {
      'Pending Review': Colors.orange,
      'Approved': Colors.green,
      'Rejected': Colors.red,
      'Active': Colors.blue,
    };
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[300],
          ),
          child: const Icon(Icons.home, color: Colors.grey),
        ),
        title: Text('Property ${index + 1}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Owner: Seller ${index + 1} • \$${(500000 + index * 50000).toString()}'),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: statusColors[status]!.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    status,
                    style: TextStyle(
                      color: statusColors[status],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text('Listed ${index + 1} days ago', style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('View Details')),
            if (status == 'Pending Review') ...[
              const PopupMenuItem(value: 'approve', child: Text('Approve')),
              const PopupMenuItem(value: 'reject', child: Text('Reject')),
            ],
            const PopupMenuItem(value: 'flag', child: Text('Flag for Review')),
            const PopupMenuItem(value: 'remove', child: Text('Remove Listing')),
          ],
          onSelected: (value) => _handlePropertyManagementAction(value, index),
        ),
        onTap: () => _showPropertyManagementDetails(context, index),
      ),
    );
  }

  Widget _buildAnalyticsTab(BuildContext context, User user, PropertyProvider propertyProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Platform Analytics',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildAnalyticsGrid(),
          const SizedBox(height: 16),
          _buildUserGrowthChart(),
          const SizedBox(height: 16),
          _buildPropertyTypeChart(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildAnalyticsCard('Daily Active Users', '1,247', Icons.people, Colors.blue),
        _buildAnalyticsCard('Conversion Rate', '3.2%', Icons.trending_up, Colors.green),
        _buildAnalyticsCard('Avg. Session Time', '12m 34s', Icons.timer, Colors.orange),
        _buildAnalyticsCard('Bounce Rate', '24%', Icons.exit_to_app, Colors.red),
      ],
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserGrowthChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'User Growth',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: false),
                  titlesData: FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: [
                        const FlSpot(0, 100),
                        const FlSpot(1, 150),
                        const FlSpot(2, 200),
                        const FlSpot(3, 280),
                        const FlSpot(4, 350),
                        const FlSpot(5, 420),
                      ],
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      dotData: FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyTypeChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Property Types Distribution',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(value: 40, color: Colors.blue, title: 'Houses'),
                    PieChartSectionData(value: 30, color: Colors.green, title: 'Apartments'),
                    PieChartSectionData(value: 20, color: Colors.orange, title: 'Condos'),
                    PieChartSectionData(value: 10, color: Colors.purple, title: 'Others'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _exportData(BuildContext context) {
    // Export data functionality
  }

  void _showNotifications(BuildContext context) {
    // Show notifications
  }

  void _generateReport(BuildContext context) {
    // Generate report
  }

  void _systemSettings(BuildContext context) {
    // System settings
  }

  void _showUserFilters(BuildContext context) {
    // Show user filters
  }

  void _handleUserAction(String action, int index) {
    // Handle user actions
  }

  void _showUserDetails(BuildContext context, int index) {
    // Show user details
  }

  void _handlePropertyManagementAction(String action, int index) {
    // Handle property management actions
  }

  void _showPropertyManagementDetails(BuildContext context, int index) {
    // Show property management details
  }

  Widget _buildSettingsTab(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'System Settings',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          _buildSystemSettings(),
          const SizedBox(height: 24),
          _buildUserManagement(),
          const SizedBox(height: 24),
          _buildAccountActions(context),
        ],
      ),
    );
  }

  Widget _buildSystemSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Platform Settings',
              'Configure platform-wide settings',
              Icons.settings_outlined,
              () => _managePlatformSettings(context),
            ),
            _buildSettingItem(
              'Security Settings',
              'Manage security and authentication',
              Icons.security_outlined,
              () => _manageSecuritySettings(context),
            ),
            _buildSettingItem(
              'Email Templates',
              'Configure system email templates',
              Icons.email_outlined,
              () => _manageEmailTemplates(context),
            ),
            _buildSettingItem(
              'Backup & Recovery',
              'Manage system backups',
              Icons.backup_outlined,
              () => _manageBackups(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserManagement() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'User Management',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Role Permissions',
              'Configure user role permissions',
              Icons.admin_panel_settings_outlined,
              () => _manageRolePermissions(context),
            ),
            _buildSettingItem(
              'User Verification',
              'Manage user verification process',
              Icons.verified_user_outlined,
              () => _manageUserVerification(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: DashboardNavigationHelper.getPrimaryColor(UserRole.admin)),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildAccountActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Account',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[50],
                  foregroundColor: Colors.red[700],
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _managePlatformSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Platform settings coming soon')),
    );
  }

  void _manageSecuritySettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Security settings coming soon')),
    );
  }

  void _manageEmailTemplates(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Email templates coming soon')),
    );
  }

  void _manageBackups(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Backup management coming soon')),
    );
  }

  void _manageRolePermissions(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Role permissions coming soon')),
    );
  }

  void _manageUserVerification(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('User verification coming soon')),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<UserProvider>().logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
