import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/analytics.dart';
import '../models/tour.dart';
import '../models/offer.dart';
import '../models/user.dart';
import '../models/property.dart';

class DashboardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Analytics Services
  Future<PropertyAnalytics?> getPropertyAnalytics(String propertyId) async {
    try {
      final doc = await _firestore.collection('property_analytics').doc(propertyId).get();
      if (doc.exists) {
        return PropertyAnalytics.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting property analytics: $e');
      return null;
    }
  }

  Future<void> updatePropertyAnalytics(PropertyAnalytics analytics) async {
    try {
      await _firestore.collection('property_analytics').doc(analytics.propertyId).set(analytics.toJson());
    } catch (e) {
      print('Error updating property analytics: $e');
    }
  }

  Future<void> recordPropertyView(String propertyId, String userId, String source) async {
    try {
      final viewData = ViewData(
        userId: userId,
        timestamp: DateTime.now(),
        source: source,
      );

      // Update analytics
      final analytics = await getPropertyAnalytics(propertyId);
      if (analytics != null) {
        final updatedAnalytics = PropertyAnalytics(
          propertyId: propertyId,
          views: analytics.views + 1,
          inquiries: analytics.inquiries,
          favorites: analytics.favorites,
          shares: analytics.shares,
          viewHistory: [...analytics.viewHistory, viewData],
          inquiryHistory: analytics.inquiryHistory,
          lastUpdated: DateTime.now(),
        );
        await updatePropertyAnalytics(updatedAnalytics);
      } else {
        // Create new analytics
        final newAnalytics = PropertyAnalytics(
          propertyId: propertyId,
          views: 1,
          inquiries: 0,
          favorites: 0,
          shares: 0,
          viewHistory: [viewData],
          inquiryHistory: [],
          lastUpdated: DateTime.now(),
        );
        await updatePropertyAnalytics(newAnalytics);
      }
    } catch (e) {
      print('Error recording property view: $e');
    }
  }

  Future<DashboardStats> getDashboardStats(String userId, UserRole role) async {
    try {
      // This would be implemented based on the user role
      // For now, returning mock data
      return DashboardStats(
        totalProperties: 25,
        activeListings: 18,
        soldProperties: 7,
        totalViews: 1234,
        totalInquiries: 45,
        totalRevenue: 2500000,
        averagePrice: 650000,
        propertiesByType: {
          'house': 12,
          'apartment': 8,
          'condo': 3,
          'townhouse': 2,
        },
        propertiesByLocation: {
          'Downtown': 10,
          'Suburbs': 8,
          'Waterfront': 5,
          'Hills': 2,
        },
        monthlyData: [
          MonthlyData(month: 'Jan', properties: 5, sales: 2, revenue: 400000, views: 200, inquiries: 15),
          MonthlyData(month: 'Feb', properties: 3, sales: 1, revenue: 250000, views: 180, inquiries: 12),
          MonthlyData(month: 'Mar', properties: 4, sales: 2, revenue: 500000, views: 250, inquiries: 18),
          MonthlyData(month: 'Apr', properties: 6, sales: 1, revenue: 300000, views: 300, inquiries: 20),
          MonthlyData(month: 'May', properties: 4, sales: 1, revenue: 350000, views: 280, inquiries: 16),
          MonthlyData(month: 'Jun', properties: 3, sales: 0, revenue: 0, views: 220, inquiries: 14),
        ],
      );
    } catch (e) {
      print('Error getting dashboard stats: $e');
      return DashboardStats(
        totalProperties: 0,
        activeListings: 0,
        soldProperties: 0,
        totalViews: 0,
        totalInquiries: 0,
        totalRevenue: 0,
        averagePrice: 0,
        propertiesByType: {},
        propertiesByLocation: {},
        monthlyData: [],
      );
    }
  }

  // Tour Services
  Future<List<PropertyTour>> getUserTours(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('tours')
          .where('buyerId', isEqualTo: userId)
          .orderBy('scheduledDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PropertyTour.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting user tours: $e');
      return [];
    }
  }

  Future<List<PropertyTour>> getAgentTours(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('tours')
          .where('agentId', isEqualTo: agentId)
          .orderBy('scheduledDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => PropertyTour.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting agent tours: $e');
      return [];
    }
  }

  Future<void> scheduleTour(PropertyTour tour) async {
    try {
      await _firestore.collection('tours').doc(tour.id).set(tour.toJson());
    } catch (e) {
      print('Error scheduling tour: $e');
    }
  }

  Future<void> updateTourStatus(String tourId, TourStatus status) async {
    try {
      await _firestore.collection('tours').doc(tourId).update({
        'status': status.toString().split('.').last,
        'completedAt': status == TourStatus.completed ? DateTime.now().toIso8601String() : null,
      });
    } catch (e) {
      print('Error updating tour status: $e');
    }
  }

  // Saved Search Services
  Future<List<SavedSearch>> getUserSavedSearches(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('saved_searches')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => SavedSearch.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting saved searches: $e');
      return [];
    }
  }

  Future<void> saveSavedSearch(SavedSearch savedSearch) async {
    try {
      await _firestore.collection('saved_searches').doc(savedSearch.id).set(savedSearch.toJson());
    } catch (e) {
      print('Error saving search: $e');
    }
  }

  Future<void> deleteSavedSearch(String searchId) async {
    try {
      await _firestore.collection('saved_searches').doc(searchId).delete();
    } catch (e) {
      print('Error deleting saved search: $e');
    }
  }

  // Lead Services
  Future<List<Lead>> getAgentLeads(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('leads')
          .where('agentId', isEqualTo: agentId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Lead.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting agent leads: $e');
      return [];
    }
  }

  Future<void> createLead(Lead lead) async {
    try {
      await _firestore.collection('leads').doc(lead.id).set(lead.toJson());
    } catch (e) {
      print('Error creating lead: $e');
    }
  }

  Future<void> updateLeadStatus(String leadId, LeadStatus status) async {
    try {
      await _firestore.collection('leads').doc(leadId).update({
        'status': status.toString().split('.').last,
        'lastContact': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error updating lead status: $e');
    }
  }

  // Offer Services
  Future<List<PropertyOffer>> getPropertyOffers(String propertyId) async {
    try {
      final querySnapshot = await _firestore
          .collection('offers')
          .where('propertyId', isEqualTo: propertyId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PropertyOffer.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting property offers: $e');
      return [];
    }
  }

  Future<List<PropertyOffer>> getBuyerOffers(String buyerId) async {
    try {
      final querySnapshot = await _firestore
          .collection('offers')
          .where('buyerId', isEqualTo: buyerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PropertyOffer.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting buyer offers: $e');
      return [];
    }
  }

  Future<void> submitOffer(PropertyOffer offer) async {
    try {
      await _firestore.collection('offers').doc(offer.id).set(offer.toJson());
    } catch (e) {
      print('Error submitting offer: $e');
    }
  }

  Future<void> updateOfferStatus(String offerId, OfferStatus status, {String? counterOfferAmount, String? rejectionReason}) async {
    try {
      final updateData = {
        'status': status.toString().split('.').last,
        'respondedAt': DateTime.now().toIso8601String(),
      };

      if (counterOfferAmount != null) {
        updateData['counterOfferAmount'] = counterOfferAmount;
      }

      if (rejectionReason != null) {
        updateData['rejectionReason'] = rejectionReason;
      }

      await _firestore.collection('offers').doc(offerId).update(updateData);
    } catch (e) {
      print('Error updating offer status: $e');
    }
  }

  // Commission Services
  Future<List<Commission>> getAgentCommissions(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .orderBy('saleDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Commission.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting agent commissions: $e');
      return [];
    }
  }

  Future<void> createCommission(Commission commission) async {
    try {
      await _firestore.collection('commissions').doc(commission.id).set(commission.toJson());
    } catch (e) {
      print('Error creating commission: $e');
    }
  }

  Future<void> updateCommissionStatus(String commissionId, CommissionStatus status) async {
    try {
      final updateData = {
        'status': status.toString().split('.').last,
      };

      if (status == CommissionStatus.paid) {
        updateData['paidDate'] = DateTime.now().toIso8601String();
      }

      await _firestore.collection('commissions').doc(commissionId).update(updateData);
    } catch (e) {
      print('Error updating commission status: $e');
    }
  }

  // Inquiry Services
  Future<List<InquiryData>> getPropertyInquiries(String propertyId) async {
    try {
      final querySnapshot = await _firestore
          .collection('inquiries')
          .where('propertyId', isEqualTo: propertyId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => InquiryData.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting property inquiries: $e');
      return [];
    }
  }

  Future<void> submitInquiry(InquiryData inquiry, String propertyId) async {
    try {
      await _firestore.collection('inquiries').doc(inquiry.id).set(inquiry.toJson());

      // Update property analytics
      await recordPropertyInquiry(propertyId, inquiry.userId);
    } catch (e) {
      print('Error submitting inquiry: $e');
    }
  }

  Future<void> recordPropertyInquiry(String propertyId, String userId) async {
    try {
      final analytics = await getPropertyAnalytics(propertyId);
      if (analytics != null) {
        final updatedAnalytics = PropertyAnalytics(
          propertyId: propertyId,
          views: analytics.views,
          inquiries: analytics.inquiries + 1,
          favorites: analytics.favorites,
          shares: analytics.shares,
          viewHistory: analytics.viewHistory,
          inquiryHistory: analytics.inquiryHistory,
          lastUpdated: DateTime.now(),
        );
        await updatePropertyAnalytics(updatedAnalytics);
      }
    } catch (e) {
      print('Error recording property inquiry: $e');
    }
  }
}
