// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBdvPjaSJG_uf7tR1LjkqDzdWg2lEMOi3w',
    appId: '1:773206833890:web:d251233b824d72246c3cbb',
    messagingSenderId: '773206833890',
    projectId: 'real-estate-808c2',
    authDomain: 'real-estate-808c2.firebaseapp.com',
    storageBucket: 'real-estate-808c2.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDbAgdyxlhRIzxSmM9wr_a9VOp_7UsqzoE',
    appId: '1:773206833890:android:b4fd57dee762398d6c3cbb',
    messagingSenderId: '773206833890',
    projectId: 'real-estate-808c2',
    storageBucket: 'real-estate-808c2.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCXBKHEfrlIcwZKfR3S8GNTeUUzLfVmd78',
    appId: '1:773206833890:ios:fbd69dab077146856c3cbb',
    messagingSenderId: '773206833890',
    projectId: 'real-estate-808c2',
    storageBucket: 'real-estate-808c2.firebasestorage.app',
    iosBundleId: 'com.example.realEstate',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCXBKHEfrlIcwZKfR3S8GNTeUUzLfVmd78',
    appId: '1:773206833890:ios:fbd69dab077146856c3cbb',
    messagingSenderId: '773206833890',
    projectId: 'real-estate-808c2',
    storageBucket: 'real-estate-808c2.firebasestorage.app',
    iosBundleId: 'com.example.realEstate',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBdvPjaSJG_uf7tR1LjkqDzdWg2lEMOi3w',
    appId: '1:773206833890:web:961992062d7886696c3cbb',
    messagingSenderId: '773206833890',
    projectId: 'real-estate-808c2',
    authDomain: 'real-estate-808c2.firebaseapp.com',
    storageBucket: 'real-estate-808c2.firebasestorage.app',
  );
}
