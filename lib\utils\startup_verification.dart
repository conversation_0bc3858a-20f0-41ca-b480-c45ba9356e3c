import 'package:flutter/foundation.dart';
import '../providers/user_provider.dart';
import '../providers/app_provider.dart';
import '../providers/property_provider.dart';
import '../providers/dashboard_provider.dart';

class StartupVerification {
  static Future<bool> verifyAppStartup({
    required UserProvider userProvider,
    required AppProvider appProvider,
    required PropertyProvider propertyProvider,
    required DashboardProvider dashboardProvider,
  }) async {
    try {
      // Verify app provider initialization
      if (!appProvider.isInitialized) {
        debugPrint('StartupVerification: AppProvider not initialized');
        return false;
      }

      // Verify user provider state
      if (userProvider.isLoading) {
        debugPrint('StartupVerification: UserProvider still loading');
        return false;
      }

      // Verify property provider initialization
      if (propertyProvider.isLoading) {
        debugPrint('StartupVerification: PropertyProvider still loading');
        return false;
      }

      debugPrint('StartupVerification: All providers initialized successfully');
      debugPrint('StartupVerification: User authenticated: ${userProvider.isAuthenticated}');
      debugPrint('StartupVerification: Current user: ${userProvider.currentUser?.name ?? 'None'}');
      
      return true;
    } catch (e) {
      debugPrint('StartupVerification: Error during verification: $e');
      return false;
    }
  }

  static Map<String, dynamic> getStartupDiagnostics({
    required UserProvider userProvider,
    required AppProvider appProvider,
    required PropertyProvider propertyProvider,
    required DashboardProvider dashboardProvider,
  }) {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'app_provider': {
        'initialized': appProvider.isInitialized,
        'loading': appProvider.isLoading,
        'theme_mode': appProvider.themeMode.toString(),
      },
      'user_provider': {
        'loading': userProvider.isLoading,
        'authenticated': userProvider.isAuthenticated,
        'has_user': userProvider.currentUser != null,
        'user_role': userProvider.currentUser?.role.toString(),
      },
      'property_provider': {
        'loading': propertyProvider.isLoading,
        'properties_count': propertyProvider.properties.length,
      },
      'dashboard_provider': {
        'loading': dashboardProvider.isLoading,
        'has_stats': dashboardProvider.dashboardStats != null,
      },
    };
  }
}
