import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/user.dart';
import '../services/api_service.dart'; // Keep for property-related services
import '../services/api_service.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;

class UserProvider extends ChangeNotifier {
  final FirebaseService _firebaseService = FirebaseService();
  // You might still need ApiService for non-user related calls, e.g. properties
  // final ApiService _apiService = ApiService(); 

  User? _currentUser;
  bool _isLoading = true; // Start with loading true
  bool _isAuthenticated = false;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;

  UserProvider() {
    _firebaseService.authStateChanges.listen(_onAuthStateChanged);
  }

  Future<void> _onAuthStateChanged(auth.User? firebaseUser) async {
    if (firebaseUser == null) {
      _currentUser = null;
      _isAuthenticated = false;
    } else {
      // User is logged in, fetch details from Firestore
      _currentUser = await _firebaseService.getUserDetails(firebaseUser.uid);
      _isAuthenticated = _currentUser != null;
    }
    _isLoading = false;
    notifyListeners();
  }

  Future<String?> login(String email, String password, UserRole role) async {
    _isLoading = true;
    notifyListeners();
    try {
      final credential = await _firebaseService.signInWithEmailAndPassword(email, password);
      if (credential.user != null) {
        final userDetails = await _firebaseService.getUserDetails(credential.user!.uid);
        if (userDetails != null && userDetails.role == role) {
          _currentUser = userDetails;
          _isAuthenticated = true;
          notifyListeners();
          return null; // Success
        } else {
          await _firebaseService.signOut();
          return 'The selected role does not match your account.';
        }
      }
      return 'An unexpected error occurred. Please try again.';
    } on auth.FirebaseAuthException catch (e) {
      return e.message ?? 'An error occurred during login.';
    } catch (e) {
      print('Login error: $e');
      return 'An unexpected error occurred. Please try again.';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String?> register(
      String name, String email, String password, String phone, UserRole role) async {
    _isLoading = true;
    notifyListeners();
    try {
      final credential = await _firebaseService.signUpWithEmailAndPassword(email, password);
      if (credential.user != null) {
        await _firebaseService.createUserInFirestore(
          uid: credential.user!.uid,
          name: name,
          email: email,
          phone: phone,
          role: role,
        );
        _currentUser = await _firebaseService.getUserDetails(credential.user!.uid);
        _isAuthenticated = true;
        notifyListeners();
        return null; // Success
      }
      return 'An unexpected error occurred during registration.';
    } on auth.FirebaseAuthException catch (e) {
      return e.message ?? 'An error occurred during registration.';
    } catch (e) {
      print('Registration error: $e');
      return 'An unexpected error occurred. Please try again.';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    await _firebaseService.signOut();
    _currentUser = null;
    _isAuthenticated = false;
    notifyListeners();
  }

  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? profileImage,
  }) async {
    if (_currentUser == null) return false;

    _isLoading = true;
    notifyListeners();

    try {
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        phone: phone ?? _currentUser!.phone,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );

      await _firebaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser; // Update local user object
      return true;
    } catch (e) {
      print('Failed to update profile: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addToFavorites(String propertyId) async {
    if (_currentUser == null || _currentUser!.favoritePropertyIds.contains(propertyId)) return;

    final updatedFavorites = List<String>.from(_currentUser!.favoritePropertyIds)..add(propertyId);
    await _firebaseService.updateUserFavorites(_currentUser!.id, updatedFavorites);
    _currentUser = _currentUser!.copyWith(favoritePropertyIds: updatedFavorites);
    notifyListeners();
  }

  Future<void> removeFromFavorites(String propertyId) async {
    if (_currentUser == null) return;

    final updatedFavorites = List<String>.from(_currentUser!.favoritePropertyIds)..remove(propertyId);
    await _firebaseService.updateUserFavorites(_currentUser!.id, updatedFavorites);
    _currentUser = _currentUser!.copyWith(favoritePropertyIds: updatedFavorites);
    notifyListeners();
  }

  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser == null) return false;

    _isLoading = true;
    notifyListeners();

    try {
      await _firebaseService.updateUserPreferences(_currentUser!.id, preferences);
      _currentUser = _currentUser!.copyWith(preferences: preferences);
      return true;
    } catch (e) {
      print('Failed to update preferences: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Initialize method
  Future<void> initialize() async {
    // Wait for the initial auth state to be determined
    if (_isLoading) {
      // Create a completer to wait for the first auth state change
      final completer = Completer<void>();
      late StreamSubscription subscription;

      subscription = _firebaseService.authStateChanges.listen((user) {
        if (!completer.isCompleted) {
          completer.complete();
        }
        subscription.cancel();
      });

      // Wait for the first auth state change or timeout after 10 seconds
      await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          print('Auth state initialization timed out');
          _isLoading = false;
          notifyListeners();
        },
      );
    }
  }

  // Get user initials for display
  String getUserInitials() {
    if (_currentUser == null) return 'U';
    final names = _currentUser!.name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  // Get display name
  String getDisplayName() {
    return _currentUser?.name ?? 'User';
  }

  // Get role display name
  String getRoleDisplayName() {
    return _currentUser?.role.displayName ?? 'Unknown';
  }

  // Get favorite property IDs
  List<String> getFavoriteIds() {
    return _currentUser?.favoritePropertyIds ?? [];
  }
}
