import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import '../models/user.dart';
import '../models/property.dart';

class FirebaseService {
  final auth.FirebaseAuth _firebaseAuth = auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user stream
  Stream<auth.User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // Get current user from firebase auth
  auth.User? get currentUser => _firebaseAuth.currentUser;

  // Sign up with email and password
  Future<auth.UserCredential> signUpWithEmailAndPassword(String email, String password) async {
    return await _firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);
  }

  // Sign in with email and password
  Future<auth.UserCredential> signInWithEmailAndPassword(String email, String password) async {
    return await _firebaseAuth.signInWithEmailAndPassword(email: email, password: password);
  }

  // Sign out
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  // Create user in Firestore
  Future<void> createUserInFirestore({
    required String uid,
    required String name,
    required String email,
    required String phone,
    required UserRole role,
  }) async {
    final user = User(
      id: uid,
      name: name,
      email: email,
      phone: phone,
      role: role,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      favoritePropertyIds: [],
      preferences: UserPreferences.initial(),
    );
    await _firestore.collection('users').doc(uid).set(user.toJson());
  }

  // Get user details from Firestore
  Future<User?> getUserDetails(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return User.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting user details: $e');
      return null;
    }
  }

  // Update user profile in Firestore
  Future<void> updateUserProfile(User user) async {
    await _firestore.collection('users').doc(user.id).update(user.toJson());
  }

  Future<void> updateUserFavorites(String userId, List<String> favoritePropertyIds) async {
    await _firestore.collection('users').doc(userId).update({
      'favoritePropertyIds': favoritePropertyIds,
    });
  }

  Future<void> updateUserPreferences(String userId, UserPreferences preferences) async {
    await _firestore.collection('users').doc(userId).update({
      'preferences': preferences.toJson(),
    });
  }

  Future<List<Property>> getPropertiesByIds(List<String> ids) async {
    if (ids.isEmpty) return [];

    final properties = <Property>[];
    // In a real app, you'd use a 'whereIn' query to fetch all documents in one go.
    // For example: _firestore.collection('properties').where(FieldPath.documentId, whereIn: ids).get();
    // Simulating this by fetching one by one for now.
    for (String id in ids) {
      try {
        final property = await getPropertyById(id);
        if (property != null) {
          properties.add(property);
        }
      } catch (e) {
        print('Error fetching property with id $id: $e');
        // Continue fetching other properties
      }
    }
    return properties;
  }

  // Get property by ID
  Future<Property?> getPropertyById(String id) async {
    try {
      final doc = await _firestore.collection('properties').doc(id).get();
      if (doc.exists) {
        return Property.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting property by id: $e');
      return null;
    }
  }
}

// ApiService class for property-related operations
class ApiService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get properties with pagination and filters
  Future<List<Property>> getProperties({
    int page = 1,
    int limit = 20,
    String? location,
    PropertyType? type,
    double? minPrice,
    double? maxPrice,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection('properties');

      // Apply filters
      if (location != null && location.isNotEmpty) {
        query = query.where('location', isEqualTo: location);
      }
      if (type != null) {
        query = query.where('type', isEqualTo: type.toString().split('.').last);
      }
      if (minPrice != null) {
        query = query.where('price', isGreaterThanOrEqualTo: minPrice);
      }
      if (maxPrice != null) {
        query = query.where('price', isLessThanOrEqualTo: maxPrice);
      }

      // Apply pagination
      query = query.limit(limit);
      // Note: Firestore doesn't have offset, so we'll just use limit for now
      // In a real app, you'd use startAfter with the last document from previous page

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Property.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error getting properties: $e');
      return [];
    }
  }

  // Get featured properties
  Future<List<Property>> getFeaturedProperties() async {
    try {
      final querySnapshot = await _firestore
          .collection('properties')
          .where('isFeatured', isEqualTo: true)
          .limit(10)
          .get();

      return querySnapshot.docs
          .map((doc) => Property.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting featured properties: $e');
      return [];
    }
  }

  // Search properties
  Future<List<Property>> searchProperties(String query) async {
    try {
      // Simple text search - in a real app you'd use a proper search service
      final querySnapshot = await _firestore
          .collection('properties')
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThan: query + 'z')
          .get();

      return querySnapshot.docs
          .map((doc) => Property.fromJson(doc.data()))
          .toList();
    } catch (e) {
      print('Error searching properties: $e');
      return [];
    }
  }

  // Get property by ID
  Future<Property?> getPropertyById(String id) async {
    try {
      final doc = await _firestore.collection('properties').doc(id).get();
      if (doc.exists) {
        return Property.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting property by id: $e');
      return null;
    }
  }

  // Get properties by IDs
  Future<List<Property>> getPropertiesByIds(List<String> ids) async {
    if (ids.isEmpty) return [];

    final properties = <Property>[];
    for (String id in ids) {
      try {
        final property = await getPropertyById(id);
        if (property != null) {
          properties.add(property);
        }
      } catch (e) {
        print('Error fetching property with id $id: $e');
      }
    }
    return properties;
  }
}
