import 'dart:math' as Math;

class PropertyOffer {
  final String id;
  final String propertyId;
  final String buyerId;
  final String buyerName;
  final String buyerEmail;
  final String buyerPhone;
  final String sellerId;
  final String agentId;
  final double offerAmount;
  final double? downPayment;
  final String? financingType;
  final DateTime closingDate;
  final List<String> contingencies;
  final String? additionalTerms;
  final OfferStatus status;
  final DateTime createdAt;
  final DateTime? respondedAt;
  final String? counterOfferAmount;
  final String? rejectionReason;

  PropertyOffer({
    required this.id,
    required this.propertyId,
    required this.buyerId,
    required this.buyerName,
    required this.buyerEmail,
    required this.buyerPhone,
    required this.sellerId,
    required this.agentId,
    required this.offerAmount,
    this.downPayment,
    this.financingType,
    required this.closingDate,
    required this.contingencies,
    this.additionalTerms,
    required this.status,
    required this.createdAt,
    this.respondedAt,
    this.counterOfferAmount,
    this.rejectionReason,
  });

  factory PropertyOffer.fromJson(Map<String, dynamic> json) {
    return PropertyOffer(
      id: json['id'] ?? '',
      propertyId: json['propertyId'] ?? '',
      buyerId: json['buyerId'] ?? '',
      buyerName: json['buyerName'] ?? '',
      buyerEmail: json['buyerEmail'] ?? '',
      buyerPhone: json['buyerPhone'] ?? '',
      sellerId: json['sellerId'] ?? '',
      agentId: json['agentId'] ?? '',
      offerAmount: (json['offerAmount'] ?? 0).toDouble(),
      downPayment: json['downPayment']?.toDouble(),
      financingType: json['financingType'],
      closingDate: DateTime.parse(json['closingDate']),
      contingencies: List<String>.from(json['contingencies'] ?? []),
      additionalTerms: json['additionalTerms'],
      status: OfferStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => OfferStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt']) : null,
      counterOfferAmount: json['counterOfferAmount'],
      rejectionReason: json['rejectionReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'propertyId': propertyId,
      'buyerId': buyerId,
      'buyerName': buyerName,
      'buyerEmail': buyerEmail,
      'buyerPhone': buyerPhone,
      'sellerId': sellerId,
      'agentId': agentId,
      'offerAmount': offerAmount,
      'downPayment': downPayment,
      'financingType': financingType,
      'closingDate': closingDate.toIso8601String(),
      'contingencies': contingencies,
      'additionalTerms': additionalTerms,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'counterOfferAmount': counterOfferAmount,
      'rejectionReason': rejectionReason,
    };
  }

  String get formattedOfferAmount {
    if (offerAmount >= 1000000) {
      return '\$${(offerAmount / 1000000).toStringAsFixed(1)}M';
    } else if (offerAmount >= 1000) {
      return '\$${(offerAmount / 1000).toStringAsFixed(0)}K';
    } else {
      return '\$${offerAmount.toStringAsFixed(0)}';
    }
  }
}

enum OfferStatus {
  pending,
  accepted,
  rejected,
  counterOffer,
  withdrawn,
  expired,
}

extension OfferStatusExtension on OfferStatus {
  String get displayName {
    switch (this) {
      case OfferStatus.pending:
        return 'Pending';
      case OfferStatus.accepted:
        return 'Accepted';
      case OfferStatus.rejected:
        return 'Rejected';
      case OfferStatus.counterOffer:
        return 'Counter Offer';
      case OfferStatus.withdrawn:
        return 'Withdrawn';
      case OfferStatus.expired:
        return 'Expired';
    }
  }
}

class Commission {
  final String id;
  final String agentId;
  final String propertyId;
  final String transactionId;
  final double salePrice;
  final double commissionRate;
  final double commissionAmount;
  final double agentSplit;
  final double brokerageSplit;
  final CommissionStatus status;
  final DateTime saleDate;
  final DateTime? paidDate;
  final String? notes;

  Commission({
    required this.id,
    required this.agentId,
    required this.propertyId,
    required this.transactionId,
    required this.salePrice,
    required this.commissionRate,
    required this.commissionAmount,
    required this.agentSplit,
    required this.brokerageSplit,
    required this.status,
    required this.saleDate,
    this.paidDate,
    this.notes,
  });

  factory Commission.fromJson(Map<String, dynamic> json) {
    return Commission(
      id: json['id'] ?? '',
      agentId: json['agentId'] ?? '',
      propertyId: json['propertyId'] ?? '',
      transactionId: json['transactionId'] ?? '',
      salePrice: (json['salePrice'] ?? 0).toDouble(),
      commissionRate: (json['commissionRate'] ?? 0).toDouble(),
      commissionAmount: (json['commissionAmount'] ?? 0).toDouble(),
      agentSplit: (json['agentSplit'] ?? 0).toDouble(),
      brokerageSplit: (json['brokerageSplit'] ?? 0).toDouble(),
      status: CommissionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => CommissionStatus.pending,
      ),
      saleDate: DateTime.parse(json['saleDate']),
      paidDate: json['paidDate'] != null ? DateTime.parse(json['paidDate']) : null,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'propertyId': propertyId,
      'transactionId': transactionId,
      'salePrice': salePrice,
      'commissionRate': commissionRate,
      'commissionAmount': commissionAmount,
      'agentSplit': agentSplit,
      'brokerageSplit': brokerageSplit,
      'status': status.toString().split('.').last,
      'saleDate': saleDate.toIso8601String(),
      'paidDate': paidDate?.toIso8601String(),
      'notes': notes,
    };
  }

  String get formattedCommissionAmount {
    if (commissionAmount >= 1000000) {
      return '\$${(commissionAmount / 1000000).toStringAsFixed(1)}M';
    } else if (commissionAmount >= 1000) {
      return '\$${(commissionAmount / 1000).toStringAsFixed(0)}K';
    } else {
      return '\$${commissionAmount.toStringAsFixed(0)}';
    }
  }

  String get formattedSalePrice {
    if (salePrice >= 1000000) {
      return '\$${(salePrice / 1000000).toStringAsFixed(1)}M';
    } else if (salePrice >= 1000) {
      return '\$${(salePrice / 1000).toStringAsFixed(0)}K';
    } else {
      return '\$${salePrice.toStringAsFixed(0)}';
    }
  }
}

enum CommissionStatus {
  pending,
  approved,
  paid,
  disputed,
}

extension CommissionStatusExtension on CommissionStatus {
  String get displayName {
    switch (this) {
      case CommissionStatus.pending:
        return 'Pending';
      case CommissionStatus.approved:
        return 'Approved';
      case CommissionStatus.paid:
        return 'Paid';
      case CommissionStatus.disputed:
        return 'Disputed';
    }
  }
}

class MortgageCalculation {
  final double loanAmount;
  final double interestRate;
  final int loanTermYears;
  final double downPayment;
  final double propertyTax;
  final double insurance;
  final double pmi;
  final double monthlyPayment;
  final double totalInterest;
  final double totalPayment;

  MortgageCalculation({
    required this.loanAmount,
    required this.interestRate,
    required this.loanTermYears,
    required this.downPayment,
    required this.propertyTax,
    required this.insurance,
    required this.pmi,
    required this.monthlyPayment,
    required this.totalInterest,
    required this.totalPayment,
  });

  factory MortgageCalculation.calculate({
    required double homePrice,
    required double downPaymentPercent,
    required double interestRate,
    required int loanTermYears,
    double propertyTaxRate = 0.012,
    double insuranceRate = 0.003,
    double pmiRate = 0.005,
  }) {
    final downPayment = homePrice * (downPaymentPercent / 100);
    final loanAmount = homePrice - downPayment;
    final monthlyRate = interestRate / 100 / 12;
    final numPayments = loanTermYears * 12;
    
    final monthlyPrincipalInterest = loanAmount * 
        (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
        (Math.pow(1 + monthlyRate, numPayments) - 1);
    
    final monthlyPropertyTax = (homePrice * propertyTaxRate) / 12;
    final monthlyInsurance = (homePrice * insuranceRate) / 12;
    final monthlyPmi = downPaymentPercent < 20 ? (loanAmount * pmiRate) / 12 : 0.0;
    
    final monthlyPayment = monthlyPrincipalInterest + monthlyPropertyTax + monthlyInsurance + monthlyPmi;
    final totalPayment = monthlyPrincipalInterest * numPayments;
    final totalInterest = totalPayment - loanAmount;

    return MortgageCalculation(
      loanAmount: loanAmount,
      interestRate: interestRate,
      loanTermYears: loanTermYears,
      downPayment: downPayment,
      propertyTax: monthlyPropertyTax,
      insurance: monthlyInsurance,
      pmi: monthlyPmi,
      monthlyPayment: monthlyPayment,
      totalInterest: totalInterest,
      totalPayment: totalPayment,
    );
  }
}
